{"format": 1, "restore": {"D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceFacade.Wcf\\RIB.Visual.Usermanagement.BoschIDM.ServiceFacade.Wcf.csproj": {}}, "projects": {"D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceDomain\\RIB.Visual.UserManagement.BoschIDM.ServiceDomain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceDomain\\RIB.Visual.UserManagement.BoschIDM.ServiceDomain.csproj", "projectName": "RIB.Visual.UserManagement.BoschIDM.ServiceDomain", "projectPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceDomain\\RIB.Visual.UserManagement.BoschIDM.ServiceDomain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceDomain\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"dependencies": {"NLog": {"target": "Package", "version": "[5.2.5, )"}}}}}, "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceFacade.Wcf\\RIB.Visual.Usermanagement.BoschIDM.ServiceFacade.Wcf.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceFacade.Wcf\\RIB.Visual.Usermanagement.BoschIDM.ServiceFacade.Wcf.csproj", "projectName": "RIB.Visual.Usermanagement.BoschIDM.ServiceFacade.Wcf", "projectPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceFacade.Wcf\\RIB.Visual.Usermanagement.BoschIDM.ServiceFacade.Wcf.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceFacade.Wcf\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {"D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceDomain\\RIB.Visual.UserManagement.BoschIDM.ServiceDomain.csproj": {"projectPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceDomain\\RIB.Visual.UserManagement.BoschIDM.ServiceDomain.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"dependencies": {"NLog": {"target": "Package", "version": "[5.2.5, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}}}}}}}