{"version": 3, "targets": {".NETFramework,Version=v4.8": {}}, "libraries": {}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": ["Microsoft.AspNet.Mvc >= 5.2.7", "Microsoft.AspNet.Razor >= 3.2.7", "Microsoft.AspNet.Web.Optimization >= 1.1.3", "Microsoft.AspNet.WebApi >= 5.2.7", "Microsoft.AspNet.WebPages >= 3.2.7", "Microsoft.Web.Infrastructure >= 1.0.0", "Newtonsoft.Json >= 13.0.1", "System.Net.Http >= 4.3.4", "WebGrease >= 1.6.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\reporting\\Platform.Client.FastReport.Web\\RIB.Visual.Reporting.Platform.Client.FastReport.Web.csproj", "projectName": "RIB.Visual.Reporting.Platform.Client.FastReport.Web", "projectPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\reporting\\Platform.Client.FastReport.Web\\RIB.Visual.Reporting.Platform.Client.FastReport.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\reporting\\Platform.Client.FastReport.Web\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"Microsoft.AspNet.Mvc": {"target": "Package", "version": "[5.2.7, )"}, "Microsoft.AspNet.Razor": {"target": "Package", "version": "[3.2.7, )"}, "Microsoft.AspNet.Web.Optimization": {"target": "Package", "version": "[1.1.3, )"}, "Microsoft.AspNet.WebApi": {"target": "Package", "version": "[5.2.7, )"}, "Microsoft.AspNet.WebPages": {"target": "Package", "version": "[3.2.7, )"}, "Microsoft.Web.Infrastructure": {"target": "Package", "version": "[1.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}, "WebGrease": {"target": "Package", "version": "[1.6.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json.\r\n  Response status code does not indicate success: 401 (Unauthorized).", "libraryId": "Newtonsoft.Json"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json.\r\n  Response status code does not indicate success: 401 (Unauthorized).", "libraryId": "Microsoft.AspNet.Mvc"}]}