{"version": 2, "dgSpecHash": "UO3/ybGZxXI=", "success": false, "projectFilePath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceFacade.Wcf\\RIB.Visual.Usermanagement.BoschIDM.ServiceFacade.Wcf.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json.\r\n  Response status code does not indicate success: 401 (Unauthorized).", "libraryId": "Newtonsoft.Json"}]}