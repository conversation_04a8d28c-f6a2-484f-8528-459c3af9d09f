{"version": 2, "dgSpecHash": "NInH8dyHEy0=", "success": false, "projectFilePath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\productionplanning\\FormworkType\\FormworkType.UnitTests\\RIB.Visual.ProductionPlanning.FormworkType.UnitTests.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json.\r\n  Response status code does not indicate success: 401 (Unauthorized).", "libraryId": "System.Data.SqlClient"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json.\r\n  Response status code does not indicate success: 401 (Unauthorized).", "libraryId": "Microsoft.NET.Test.Sdk"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json.\r\n  Response status code does not indicate success: 401 (Unauthorized).", "libraryId": "xunit.runner.visualstudio"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json.\r\n  Response status code does not indicate success: 401 (Unauthorized).", "libraryId": "XunitXml.TestLogger"}]}