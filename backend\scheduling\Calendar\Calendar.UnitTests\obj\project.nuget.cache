{"version": 2, "dgSpecHash": "fuvJFPiZYdA=", "success": false, "projectFilePath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\scheduling\\Calendar\\Calendar.UnitTests\\RIB.Visual.Scheduling.Calendar.UnitTests.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json.\r\n  Response status code does not indicate success: 401 (Unauthorized).", "libraryId": "Microsoft.NET.Test.Sdk"}]}