{"version": 3, "targets": {"net8.0": {"RIB.Visual.ProductionPlanning.Configuration.Core/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "compile": {"bin/placeholder/RIB.Visual.ProductionPlanning.Configuration.Core.dll": {}}, "runtime": {"bin/placeholder/RIB.Visual.ProductionPlanning.Configuration.Core.dll": {}}}}}, "libraries": {"RIB.Visual.ProductionPlanning.Configuration.Core/1.0.0": {"type": "project", "path": "../Configuration.Core/RIB.Visual.ProductionPlanning.Configuration.Core.csproj", "msbuildProject": "../Configuration.Core/RIB.Visual.ProductionPlanning.Configuration.Core.csproj"}}, "projectFileDependencyGroups": {"net8.0": ["RIB.Visual.ProductionPlanning.Configuration.Core >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\productionplanning\\Configuration\\Configuration.Common\\RIB.Visual.ProductionPlanning.Configuration.Common.csproj", "projectName": "RIB.Visual.ProductionPlanning.Configuration.Common", "projectPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\productionplanning\\Configuration\\Configuration.Common\\RIB.Visual.ProductionPlanning.Configuration.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\productionplanning\\Configuration\\Configuration.Common\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\productionplanning\\Configuration\\Configuration.Core\\RIB.Visual.ProductionPlanning.Configuration.Core.csproj": {"projectPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\productionplanning\\Configuration\\Configuration.Core\\RIB.Visual.ProductionPlanning.Configuration.Core.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "centralPackageVersions": {"AngleSharp": "0.17.1", "AngleSharp.Css": "0.17.0", "Azure.AI.Vision.ImageAnalysis": "1.0.0", "Azure.Monitor.OpenTelemetry.Exporter": "1.4.0", "Azure.Storage.Blobs": "12.24.1", "Azure.Storage.Common": "12.23.0", "Azure.Storage.Files.Shares": "12.22.0", "ClosedXML": "0.102.3", "CommandLineParser": "2.9.1", "CoreWCF.Http": "1.7.0", "CoreWCF.NetTcp": "1.7.0", "CoreWCF.Primitives": "1.7.0", "coverlet.collector": "6.0.4", "DeepEqual": "5.1.0", "DocumentFormat.OpenXml": "2.20.0", "EntityFramework": "6.5.1", "EntityFramework.Functions": "1.5.0", "EntityFrameworkExtras.EF6": "3.0.5", "FluentAssertions": "7.1.0", "GaebToolbox": "2024.104.102", "H.Formatters": "15.0.0", "H.Formatters.BinaryFormatter": "15.0.0", "H.Formatters.Newtonsoft.Json": "15.0.0", "H.Pipes": "15.0.0", "H.Pipes.AccessControl": "15.0.0", "HtmlAgilityPack": "1.12.1", "HtmlSanitizer": "9.0.886", "Humanizer": "2.14.1", "IdentityModel": "7.0.0", "IdentityModel.AspNetCore": "4.3.0", "IdentityModel.AspNetCore.OAuth2Introspection": "6.2.0", "J2N": "2.1.0", "Jint": "2.11.58", "JWT": "11.0.0", "Kveer.XmlRPC": "1.3.1", "Lucene.Net": "4.8.0-beta00017", "Lucene.Net.Analysis.Common": "4.8.0-beta00017", "Lucene.Net.QueryParser": "4.8.0-beta00017", "MathNet.Numerics": "5.0.0", "Microsoft.ApplicationInsights.AspNetCore": "2.23.0", "Microsoft.ApplicationInsights.DependencyCollector": "2.23.0", "Microsoft.ApplicationInsights.SnapshotCollector": "1.4.6", "Microsoft.AspNet.WebApi.Client": "6.0.0", "Microsoft.AspNetCore": "2.3.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.12", "Microsoft.AspNetCore.Connections.Abstractions": "8.0.12", "Microsoft.AspNetCore.Hosting": "2.3.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.3.0", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.3.0", "Microsoft.AspNetCore.Http": "2.3.0", "Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.AspNetCore.Http.Extensions": "2.3.0", "Microsoft.AspNetCore.Http.Features": "5.0.17", "Microsoft.AspNetCore.JsonPatch": "8.0.12", "Microsoft.AspNetCore.Mvc": "2.3.0", "Microsoft.AspNetCore.Mvc.Core": "2.3.0", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.12", "Microsoft.AspNetCore.Mvc.WebApiCompatShim": "2.3.0", "Microsoft.AspNetCore.OData": "7.7.4", "Microsoft.AspNetCore.Routing": "2.3.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.3.0", "Microsoft.AspNetCore.Server.Kestrel": "2.3.0", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.3.0", "Microsoft.AspNetCore.TestHost": "8.0.12", "Microsoft.AspNetCore.WebSockets": "2.3.0", "Microsoft.AspNetCore.WebUtilities": "8.0.12", "Microsoft.Bcl.AsyncInterfaces": "9.0.7", "Microsoft.ClearScript": "7.5.0", "Microsoft.CSharp": "4.7.0", "Microsoft.Data.SqlClient": "6.0.2", "Microsoft.Data.Sqlite.Core": "9.0.7", "Microsoft.Exchange.WebServices.NETStandard": "1.1.3", "Microsoft.Extensions.Configuration": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.7", "Microsoft.Extensions.Configuration.Json": "9.0.7", "Microsoft.Extensions.DependencyInjection": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "Microsoft.Extensions.ObjectPool": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Microsoft.Graph": "4.54.0", "Microsoft.Graph.Core": "2.0.15", "Microsoft.Identity.Client": "4.73.1", "Microsoft.Net.Http.Headers": "8.0.12", "Microsoft.NET.Test.Sdk": "17.14.1", "Microsoft.OData.Client": "7.20.0", "Microsoft.OData.Core": "7.20.0", "Microsoft.OData.Edm": "7.20.0", "Microsoft.PowerBI.Api": "4.22.0", "Microsoft.Rest.ClientRuntime": "3.0.3", "Microsoft.SharePointOnline.CSOM": "16.1.26211.12010", "Microsoft.VisualStudio.TestPlatform": "********", "Microsoft.Web.Administration": "11.1.0", "Microsoft.Win32.Registry": "6.0.0-preview.5.21301.5", "MimeKit": "4.13.0", "MixERP.Net.VCards": "1.0.7", "Moq": "4.20.72", "MsgReader": "4.4.16", "MSTest.TestFramework": "3.9.3", "Newtonsoft.Json": "13.0.3", "Newtonsoft.Json.Bson": "1.0.3", "NLog": "5.5.0", "NLog.Database": "5.5.0", "NLog.Extensions.Logging": "5.5.0", "NLog.Web.AspNetCore": "5.5.0", "NLog.WindowsEventLog": "5.5.0", "NReco.PdfGenerator.LT": "1.2.1", "OpenTelemetry": "1.12.0", "OpenTelemetry.Extensions.Hosting": "1.12.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.SqlClient": "1.9.0-beta.1", "PdfSharp": "6.1.1", "PdfSharpCore": "1.3.67", "Pipelines.Sockets.Unofficial": "2.2.16", "PnP.Core": "1.15.0", "PnP.Framework": "1.18.0", "ProxyInterfaceGenerator": "0.4.0", "Rebex.Elliptic.Ed25519": "1.2.2", "ResXResourceReader.NetStandard": "1.3.0", "RIB.CpiComponents": "25.0.36401", "RIB.FastReport.Core": "2025.1.14.1", "RIB.FastReport.Core.Blazor": "2025.1.14.1", "RIB.FastReport.Data.MsSql": "2025.1.14.1", "RIB.FastReport.Net": "2025.1.14.1", "RIB.Qto": "25.0.36400", "SharpZipLib": "1.4.2", "SixLabors.ImageSharp": "2.1.10", "Squid-Box.SevenZipSharp": "********", "StackExchange.Redis": "2.8.41", "System.Collections.Concurrent": "4.3.0", "System.Collections.Immutable": "8.0.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Annotations": "5.0.0", "System.ComponentModel.Composition": "8.0.0", "System.ComponentModel.Composition.Registration": "8.0.0", "System.ComponentModel.Primitives": "4.3.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Configuration.Abstractions": "********", "System.Configuration.ConfigurationManager": "9.0.7", "System.Data.Common": "4.3.0", "System.Data.OleDb": "8.0.0", "System.Data.SqlClient": "4.9.0", "System.Data.SQLite": "1.0.119", "System.Diagnostics.DiagnosticSource": "9.0.7", "System.Diagnostics.PerformanceCounter": "9.0.7", "System.DirectoryServices": "9.0.7", "System.DirectoryServices.AccountManagement": "9.0.7", "System.Drawing.Common": "9.0.7", "System.IdentityModel.Tokens.Jwt": "8.12.1", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.Hashing": "9.0.7", "System.IO.Packaging": "9.0.7", "System.IO.Pipes": "4.3.0", "System.IO.Pipes.AccessControl": "6.0.0-preview.5.21301.5", "System.Net.Http": "4.3.4", "System.Numerics.Vectors": "4.6.1", "System.Private.ServiceModel": "4.10.3", "System.Runtime": "4.3.1", "System.Runtime.Caching": "9.0.7", "System.Runtime.CompilerServices.Unsafe": "6.1.2", "System.Security.Cryptography.Cng": "6.0.0-preview.4.21253.7", "System.Security.Cryptography.Pkcs": "9.0.7", "System.Security.Cryptography.ProtectedData": "9.0.7", "System.Security.Cryptography.Xml": "9.0.7", "System.Security.Principal.Windows": "6.0.0-preview.5.21301.5", "System.ServiceModel.Duplex": "6.0.0", "System.ServiceModel.Http": "6.2.0", "System.ServiceModel.NetTcp": "6.2.0", "System.ServiceModel.Primitives": "6.2.0", "System.ServiceModel.Security": "6.0.0", "System.ServiceModel.Syndication": "9.0.7", "System.ServiceProcess.ServiceController": "9.0.7", "System.Text.Encoding.CodePages": "9.0.7", "System.Text.Json": "9.0.7", "System.Threading.Channels": "9.0.7", "System.Threading.Tasks.Extensions": "4.5.4", "System.Xml.XDocument": "4.3.0", "System.Xml.XPath.XDocument": "4.3.0", "TiffLibrary.ImageSharpAdapter": "0.6.65", "TopShelf.ServiceInstaller": "4.3.0", "xunit": "2.9.3", "xunit.abstractions": "2.0.3", "xunit.analyzers": "1.22.0", "xunit.assert": "2.9.3", "xunit.core": "2.9.3", "xunit.extensibility.core": "2.9.3", "xunit.extensibility.execution": "2.9.3", "xunit.runner.console": "2.9.3", "xunit.runner.visualstudio": "3.1.1", "XunitXml.TestLogger": "6.1.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}