{"version": 3, "targets": {".NETFramework,Version=v4.8": {}, ".NETFramework,Version=v4.8/win-x86": {}}, "libraries": {}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": ["<PERSON>.<PERSON>ers.Newtonsoft.Json >= 12.0.21", "<PERSON><PERSON> >= 2.0.21", "NLog >= 5.0.0-preview.3", "Newtonsoft.Json >= 13.0.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\reporting\\Platform.FastReport.GetParameter\\RIB.Visual.Reporting.FastReport.GetParameter.csproj", "projectName": "RIB.Visual.Reporting.FastReport.GetParameter", "projectPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\reporting\\Platform.FastReport.GetParameter\\RIB.Visual.Reporting.FastReport.GetParameter.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\reporting\\Platform.FastReport.GetParameter\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {"D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\reporting\\Platform.Common\\RIB.Visual.Reporting.Platform.Common.csproj": {"projectPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\reporting\\Platform.Common\\RIB.Visual.Reporting.Platform.Common.csproj"}, "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\reporting\\Platform.Core\\RIB.Visual.Reporting.Platform.Core.csproj": {"projectPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\reporting\\Platform.Core\\RIB.Visual.Reporting.Platform.Core.csproj"}, "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\reporting\\Platform.ServiceDomain\\RIB.Visual.Reporting.Platform.ServiceDomain.csproj": {"projectPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\reporting\\Platform.ServiceDomain\\RIB.Visual.Reporting.Platform.ServiceDomain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"H.Formatters.Newtonsoft.Json": {"target": "Package", "version": "[12.0.21, )"}, "H.Pipes": {"target": "Package", "version": "[2.0.21, )"}, "NLog": {"target": "Package", "version": "[5.0.0-preview.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}, "logs": [{"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json.\r\n  Response status code does not indicate success: 401 (Unauthorized).", "libraryId": "<PERSON><PERSON>"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json.\r\n  Response status code does not indicate success: 401 (Unauthorized).", "libraryId": "H.Formatters.Newtonsoft.Json"}]}