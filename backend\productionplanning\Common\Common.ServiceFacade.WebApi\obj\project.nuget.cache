{"version": 2, "dgSpecHash": "WEvGgqCd3IE=", "success": false, "projectFilePath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\productionplanning\\Common\\Common.ServiceFacade.WebApi\\RIB.Visual.ProductionPlanning.Common.ServiceFacade.WebApi.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json.\r\n  Response status code does not indicate success: 401 (Unauthorized).", "libraryId": "System.Runtime.Caching"}]}