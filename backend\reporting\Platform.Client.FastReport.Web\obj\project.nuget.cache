{"version": 2, "dgSpecHash": "cd7vpfC1Xv8=", "success": false, "projectFilePath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\reporting\\Platform.Client.FastReport.Web\\RIB.Visual.Reporting.Platform.Client.FastReport.Web.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json.\r\n  Response status code does not indicate success: 401 (Unauthorized).", "libraryId": "Newtonsoft.Json"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json.\r\n  Response status code does not indicate success: 401 (Unauthorized).", "libraryId": "Microsoft.AspNet.Mvc"}]}