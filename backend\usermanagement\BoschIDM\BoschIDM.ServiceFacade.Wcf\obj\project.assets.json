{"version": 3, "targets": {".NETFramework,Version=v4.8": {}}, "libraries": {}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": ["NLog >= 5.2.5", "Newtonsoft.Json >= 13.0.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceFacade.Wcf\\RIB.Visual.Usermanagement.BoschIDM.ServiceFacade.Wcf.csproj", "projectName": "RIB.Visual.Usermanagement.BoschIDM.ServiceFacade.Wcf", "projectPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceFacade.Wcf\\RIB.Visual.Usermanagement.BoschIDM.ServiceFacade.Wcf.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceFacade.Wcf\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {"D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceDomain\\RIB.Visual.UserManagement.BoschIDM.ServiceDomain.csproj": {"projectPath": "D:\\iTwo4.0_CodeBase\\Application_1\\application\\backend\\usermanagement\\BoschIDM\\BoschIDM.ServiceDomain\\RIB.Visual.UserManagement.BoschIDM.ServiceDomain.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"dependencies": {"NLog": {"target": "Package", "version": "[5.2.5, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}}}}}, "logs": [{"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json.\r\n  Response status code does not indicate success: 401 (Unauthorized).", "libraryId": "Newtonsoft.Json"}]}